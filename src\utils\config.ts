/**
 * 应用配置常量
 */

import getParamter from './getParamter';

/**
 * 从 pipelineParams 中提取 token 并自动存储到 localStorage
 * @returns 提取的 token 或默认 token
 */
function getTokenFromPipelineParams(): string {
  const defaultToken = '';

  try {
    // 从 URL 中获取 pipelineParams
    const pipelineParamsStr = getParamter('pipelineParams', window.location.href, false)?.pipelineParams;

    if (!pipelineParamsStr) {
      return defaultToken;
    }

    // 解析 pipelineParams JSON 字符串
    const pipelineParamsObj = JSON.parse(decodeURIComponent(pipelineParamsStr));

    // 从 pipelineParams 中提取 token
    if (pipelineParamsObj.token && typeof pipelineParamsObj.token === 'string') {
      // 自动将URL中的token存储到localStorage
      localStorage.setItem('myapp_token', pipelineParamsObj.token);
      return pipelineParamsObj.token;
    }

    return defaultToken;
  } catch (error) {
    console.warn('从 pipelineParams 提取 token 失败:', error);
    return defaultToken;
  }
}

// JWT Token 配置
export const JWT_CONFIG = {
  // 默认的JWT token（开发环境使用，或从 pipelineParams 中提取）
  DEFAULT_TOKEN: getTokenFromPipelineParams(),
  // localStorage中存储token的key
  TOKEN_KEY: 'myapp_token',
} as const;

// API 配置
export const API_CONFIG = {
  // 请求超时时间（毫秒）
  TIMEOUT: 10000,
  // 响应类型
  RESPONSE_TYPE: 'json',
  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
} as const;

/**
 * 获取Authorization header的值
 * 优先从localStorage获取，如果没有则使用默认token
 */
export function getAuthorizationHeader(): string {
  return localStorage.getItem(JWT_CONFIG.TOKEN_KEY) || JWT_CONFIG.DEFAULT_TOKEN;
}

/**
 * 设置token到localStorage
 */
export function setToken(token: string): void {
  localStorage.setItem(JWT_CONFIG.TOKEN_KEY, token);
}

/**
 * 清除token
 */
export function clearToken(): void {
  localStorage.removeItem(JWT_CONFIG.TOKEN_KEY);
}
